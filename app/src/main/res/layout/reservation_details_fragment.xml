<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="viewmodel"
            type="com.eatapp.clementine.ui.reservation.ReservationDetailsViewModel" />

        <import type="android.view.View" />

        <import type="android.text.InputType" />

        <import type="com.eatapp.clementine.R" />

        <import type="com.eatapp.clementine.internal.managers.OptionType" />

        <import type="android.text.TextUtils" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        android:animateLayoutChanges="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:context=".ui.reservation.ReservationDetailsFragment">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/container_scroll"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/mainBcg"
            android:fillViewport="true"
            app:layout_constraintBottom_toTopOf="@id/bottomButtons"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/mainBcg">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_list_items"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@color/white"
                    android:nestedScrollingEnabled="false"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.eatapp.clementine.views.AdvancedGuestSearchView
                    android:id="@+id/item_advanced_guest_search"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.fragment.app.FragmentContainerView
                    android:id="@+id/guest_profile_fc"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/guest_profile_divider"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/guest_profile_title_tv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="@dimen/global_margin_16"
                    android:paddingTop="@dimen/global_margin_16"
                    android:paddingBottom="@dimen/section_title_margin_bottom"
                    android:fontFamily="@font/inter_regular"
                    app:layout_constraintTop_toBottomOf="@id/rv_list_items"
                    android:text="@string/guest_details"
                    android:textAllCaps="false"
                    android:textColor="@color/grey900"
                    android:textSize="@dimen/section_title_size" />

                <View
                    android:id="@+id/guest_profile_divider"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    app:layout_constraintTop_toBottomOf="@id/guest_profile_title_tv"
                    android:background="@color/colorSeparator" />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/guest_profile_gp"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:constraint_referenced_ids="guest_profile_fc,guest_profile_divider,guest_profile_title_tv" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomButtons"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <com.eatapp.clementine.views.LoadingButton
                android:id="@+id/tableReadyBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/global_margin_16"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:background="@drawable/shape_rounded_btn_bcg_green_100"
                app:leftIcon="@drawable/ic_icon_table_ready_message_sent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:progressBarColor="@color/grey700"
                app:title="@string/table_ready"
                app:tintColor="@{R.color.white}" />

            <com.eatapp.clementine.views.LoadingButton
                android:enabled="@{!viewmodel.loading &amp;&amp; !viewmodel.create}"
                android:id="@+id/createBtn"
                android:layout_width="match_parent"
                android:layout_height="@dimen/button_height"
                android:layout_marginStart="@dimen/global_margin_16"
                android:layout_marginTop="@dimen/margin_12"
                android:layout_marginEnd="@dimen/global_margin_16"
                android:layout_marginBottom="@dimen/global_margin_16"
                android:background="@drawable/shape_rounded_btn_bcg_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/tableReadyBtn"
                app:progressBarColor="@color/white"
                app:title="@string/create_reservation"
                app:tintColor="@{R.color.white}" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>