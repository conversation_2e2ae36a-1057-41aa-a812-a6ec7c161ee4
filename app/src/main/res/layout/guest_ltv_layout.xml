<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="guest"
            type="com.eatapp.clementine.data.network.response.guest.Guest" />

        <variable
            name="createMode"
            type="Boolean" />

        <variable
            name="posActive"
            type="Boolean" />

        <variable
            name="loyaltyActive"
            type="Boolean" />

        <variable
            name="embeddedMode"
            type="Boolean" />

        <variable
            name="currency"
            type="String" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:orientation="vertical">

        <LinearLayout
            android:baselineAligned="false"
            android:paddingStart="@dimen/margin_12"
            android:paddingEnd="@dimen/margin_12"
            android:id="@+id/linearLayout"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:orientation="horizontal"
            android:gravity="center_horizontal"
            android:visibility="@{createMode ? View.GONE : View.VISIBLE, default=visible}"
            bind:layout_constraintEnd_toEndOf="parent"
            bind:layout_constraintStart_toStartOf="parent"
            bind:layout_constraintTop_toTopOf="parent">

            <LinearLayout
                visible_or_gone="@{loyaltyActive}"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.attributes.loyaltyPoints)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="12" />

                <TextView
                    android:textAlignment="center"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/loyalty_label"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.upcomingCount)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/upcoming"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.visitCount)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="2" />

                <TextView
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/materialised"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.noShowCount)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="2" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/no_shows"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.deniedCount)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/denied"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <!--            <ImageButton-->
            <!--                android:id="@+id/createReservationBtn"-->
            <!--                android:layout_width="wrap_content"-->
            <!--                android:layout_height="wrap_content"-->
            <!--                android:layout_gravity="center_vertical"-->
            <!--                android:layout_marginStart="20dp"-->
            <!--                android:layout_marginEnd="16dp"-->
            <!--                android:background="@{viewmodel.permissionReservation.boolValue ? @drawable/ic_icon_create_reservation : @drawable/ic_icon_create_reservation_disabled}"-->
            <!--                android:enabled="@{!viewmodel.isFreemium}"-->
            <!--                android:visibility="@{viewmodel.createMode || viewmodel.simplifiedMode ? View.GONE : View.VISIBLE, default=visible}" />-->

        </LinearLayout>

        <View
            android:id="@+id/view4"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            android:visibility="@{createMode || embeddedMode ? View.GONE : View.VISIBLE, default = visible}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout" />

        <LinearLayout
            android:id="@+id/linearLayout1"
            android:layout_width="0dp"
            android:layout_height="60dp"
            android:orientation="horizontal"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:visibility="@{createMode || !posActive ? View.GONE : View.VISIBLE, default=visible}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/view4">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.posTicketsCount)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/pos_tickets"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.totalSpend)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="2" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_regular"
                    android:gravity="center"
                    android:text='@{String.format("Total (%s)", currency)}'
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.averageSpendPerVisit)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/avg_per_visit"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="1"
                android:gravity="center"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:gravity="center"
                    android:text="@{String.valueOf(guest.averageSpendPerCover)}"
                    android:textColor="@color/colorDark50"
                    android:textSize="16sp"
                    tools:text="5" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_medium"
                    android:gravity="center"
                    android:text="@string/avg_per_cover"
                    android:textColor="@color/grey600"
                    android:textSize="12sp" />
            </LinearLayout>

        </LinearLayout>

        <View
            android:id="@+id/view5"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            android:visibility="@{createMode || !posActive ? View.GONE : View.VISIBLE, default=visible}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/linearLayout1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>