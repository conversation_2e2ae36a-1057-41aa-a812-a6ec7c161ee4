package com.eatapp.clementine.internal

import android.animation.AnimatorSet
import android.animation.ValueAnimator
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.os.Build
import android.os.Bundle
import android.os.Parcelable
import android.text.TextPaint
import android.util.LayoutDirection
import android.util.Patterns
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.animation.doOnEnd
import androidx.core.app.ActivityCompat
import androidx.core.net.toUri
import androidx.core.text.layoutDirection
import androidx.core.view.isVisible
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.room.Table
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.cancelChildren
import retrofit2.Response
import java.io.File
import java.net.HttpURLConnection
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit


/**
 * Live data extensions
 */

inline fun <reified T> LiveData<T>.get(): T =
    if (value != null) value!! else T::class.java.newInstance()

inline fun <reified T> MutableLiveData<T>.get(): T =
    if (value != null) value!! else T::class.java.newInstance()

fun <T> MutableLiveData<T>.asLiveData(): LiveData<T> = this
fun <T> T.asLiveData(): LiveData<T> = MutableLiveData<T>().apply { postValue(this@asLiveData) }
fun <T> T.asMutableLiveData() = MutableLiveData<T>().apply { postValue(this@asMutableLiveData) }

/**
 * Parcelable extensions
 */

inline fun <reified T : Parcelable> Bundle.parcelableArrayList(key: String): ArrayList<T>? = when {
    Build.VERSION.SDK_INT >= 33 -> getParcelableArrayList(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelableArrayList(key)
}

inline fun <reified T : Parcelable> Intent.parcelableArrayList(key: String): ArrayList<T>? = when {
    Build.VERSION.SDK_INT >= 33 -> getParcelableArrayListExtra(key, T::class.java)
    else -> @Suppress("DEPRECATION") getParcelableArrayListExtra(key)
}

/**
 * Misc extensions
 */

fun dateFromString(date: String?): Date? {
    return when (date.isNullOrBlank()) {
        true -> null
        false -> SimpleDateFormat("yyyy-MM-dd", Locale.US).parse(date)
    }
}

fun simpleDate(date: Date): String {
    return SimpleDateFormat("yyyy-M-d", Locale.US).format(date)
}

fun formatNonLocal(): SimpleDateFormat {
    return SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US)
}

fun eatDateISO8601TimezoneDateFormatter(): DateTimeFormatter {
    return DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX", Locale.US)
}

fun eatDateISO8601Timezone1DateFormatter(): SimpleDateFormat {
    return SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ", Locale.US)
}

fun eatDateISO8601ColonTZFormatter(): SimpleDateFormat {
    return SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX", Locale.US)
}

fun durationFormatted(duration: Int): String {
    val hours = TimeUnit.SECONDS.toHours(duration.toLong())
    val minutes = TimeUnit.SECONDS.toMinutes(duration.toLong()) - TimeUnit.HOURS.toMinutes(hours)

    return when {
        hours.toInt() == 0 -> String.format("%02dmin", minutes)
        minutes.toInt() == 0 && hours.toInt() == 1 -> String.format("%d hour", hours)
        minutes.toInt() == 0 && hours.toInt() > 1 -> String.format("%d hours", hours)
        else -> String.format("%dh %02dmin", hours, minutes)
    }
}

fun ceilToQuarter(date: Date): Date {

    val calendar = Calendar.getInstance()
    calendar.time = date

    val unRoundedMinutes = calendar.get(Calendar.MINUTE)
    val mod = unRoundedMinutes % 15
    calendar.add(Calendar.MINUTE, if (mod < 8) -mod else 15 - mod)
    calendar.set(Calendar.SECOND, 0)
    calendar.set(Calendar.MILLISECOND, 0)

    return calendar.time
}

fun startOfTheDayAbsolute(date: Date): Calendar {

    val c: Calendar = Calendar.getInstance()

    c.time = date

    c.set(Calendar.HOUR_OF_DAY, 0)
    c.set(Calendar.MINUTE, 0)
    c.set(Calendar.SECOND, 0)
    c.set(Calendar.MILLISECOND, 0)

    return c
}

fun startOfTheDay(date: Date): Date {

    val c: Calendar = Calendar.getInstance()

    c.time = date

    if (c.get(Calendar.HOUR_OF_DAY) < 4) {
        c.add(Calendar.DATE, -1)
    }

    c.set(Calendar.HOUR_OF_DAY, 0)
    c.set(Calendar.MINUTE, 0)
    c.set(Calendar.SECOND, 0)
    c.set(Calendar.MILLISECOND, 0)

    return c.time
}

fun startOfTheEatDay(date: Date): Date {

    val c: Calendar = Calendar.getInstance()

    c.time = date

    if (c.get(Calendar.HOUR_OF_DAY) < 4) {
        c.add(Calendar.DATE, -1)
    }

    c.set(Calendar.HOUR_OF_DAY, 4)
    c.set(Calendar.MINUTE, 0)
    c.set(Calendar.SECOND, 0)
    c.set(Calendar.MILLISECOND, 0)

    return c.time
}

fun endOfTheEatDay(date: Date): Date {

    val c: Calendar = Calendar.getInstance()

    c.time = date

    if (c.get(Calendar.HOUR_OF_DAY) < 4) {
        c.add(Calendar.DATE, -1)
    }

    c.set(Calendar.HOUR_OF_DAY, 4)
    c.set(Calendar.MINUTE, 0)
    c.set(Calendar.SECOND, 0)
    c.set(Calendar.MILLISECOND, 0)
    c.add(Calendar.DATE, 1)

    return c.time
}

fun Date.addTimeInterval(type: Int, duration: Int = 0): Date {
    val c = Calendar.getInstance()
    c.time = this
    c.add(type, duration)
    return c.time
}

fun Table.isAlreadyOccupied(
    reservation: Reservation?,
    reservations: List<Reservation>?
): Boolean {
    return hasConflictingReservation(reservation, reservations) { res ->
        Status.isSeated(res.status)
    }
}

fun Table.isAlreadyReserved(
    reservation: Reservation?,
    reservations: List<Reservation>?
): Boolean {
    return hasConflictingReservation(reservation, reservations) { res ->
        !Status.isRemoved(res.status) && !Status.isSeated(res.status)
    }
}

/**
 * Helper function to check for conflicting reservations based on a status condition
 */
private fun Table.hasConflictingReservation(
    reservation: Reservation?,
    reservations: List<Reservation>?,
    statusCondition: (Reservation) -> Boolean
): Boolean {
    if (reservation == null || reservations == null) return false

    return reservations
        .filter { it.id != reservation.id }
        .filter { it.tables?.any { table -> table.id == this.id } == true }
        .any { conflictingReservation ->
            val hasTimeOverlap = checkTimeOverlap(
                reservation,
                conflictingReservation
            )
            hasTimeOverlap && statusCondition(conflictingReservation)
        }
}

/**
 * Check if two reservations have overlapping times
 */
private fun checkTimeOverlap(
    reservation: Reservation,
    otherReservation: Reservation
): Boolean {
    val reservationEndTime =
        reservation.startTime.addTimeInterval(Calendar.SECOND, reservation.duration)
    val otherReservationEndTime =
        otherReservation.startTime.addTimeInterval(Calendar.SECOND, otherReservation.duration)

    // Check for exact time matches
    if (otherReservation.startTime == reservation.startTime || otherReservationEndTime == reservationEndTime) {
        return true
    }

    // Check for containment (one reservation completely inside another)
    val isInner =
        otherReservation.startTime.after(reservation.startTime) && otherReservationEndTime.before(
            reservationEndTime
        )
    val isOuter =
        otherReservation.startTime.before(reservation.startTime) && otherReservationEndTime.after(
            reservationEndTime
        )

    if (isInner || isOuter) {
        return true
    }

    // Check for partial overlap (end of one reservation overlaps start of another)
    val isConflicting =
        otherReservationEndTime.isBetween(reservation.startTime, reservationEndTime) ||
                otherReservation.startTime.isBetween(reservation.startTime, reservationEndTime)

    return isConflicting
}

fun isNumeric(strNum: String?): Boolean {
    return strNum?.toIntOrNull() != null
}

fun blendColors(color1: Int, color2: Int, ratio: Float): Int {
    val inverseRatio = 1 - ratio

    val a = (Color.alpha(color1) * inverseRatio + Color.alpha(color2) * ratio).toInt()
    val r = (Color.red(color1) * inverseRatio + Color.red(color2) * ratio).toInt()
    val g = (Color.green(color1) * inverseRatio + Color.green(color2) * ratio).toInt()
    val b = (Color.blue(color1) * inverseRatio + Color.blue(color2) * ratio).toInt()

    return Color.argb(a, r, g, b)
}

var View.visible: Boolean
    get() = isVisible
    set(visible) {
        visibility = if (visible)
            View.VISIBLE
        else
            View.INVISIBLE
    }

var View.visibleOrGone: Boolean
    get() = isVisible
    set(visible) {
        visibility = if (visible)
            View.VISIBLE
        else
            View.GONE
    }

fun View.screenY() = IntArray(2).also { getLocationOnScreen(it) }[1]

val Int.dp: Int
    get() = (this / Resources.getSystem().displayMetrics.density).toInt()
val Int.px: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()

val Context.isTablet: Boolean
    get() = resources.getBoolean(R.bool.is_tablet)

fun isRtl() = (Locale.getDefault().layoutDirection == LayoutDirection.RTL)

fun Date.isBetween(date1: Date, date2: Date): Boolean {
    val minDate = minOf(date1, date2)
    val maxDate = maxOf(date1, date2)
    return this in minDate..maxDate
}

fun Date.isYesterday(): Boolean {
    val inputLocalDate = this.toInstant()
        .atZone(ZoneId.systemDefault())
        .toLocalDate()

    val yesterday = LocalDate.now().minusDays(1)

    return inputLocalDate.isEqual(yesterday)
}

fun String.shortDateTime(): String {
    val inputFormatter = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX", Locale.US)
    val inputFormatter2 = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.US)
    val outputFormatter = SimpleDateFormat("dd MMM, hh:mm a", Locale.US)

    val parsedDate = try {
        inputFormatter.parse(this) ?: Date()
    } catch (e: Exception) {
        inputFormatter2.parse(this) ?: Date()
    }

    return outputFormatter.format(parsedDate)
}

fun Date.shortDateTime(): String {
    val outputFormatter = SimpleDateFormat("dd MMM, hh:mm a", Locale.US)
    return outputFormatter.format(this)
}

fun String.width(
    fontSizeSp: Float,
    fontFamily: Typeface,
    density: Float
): Float {
    val paint = TextPaint()
    paint.typeface = fontFamily
    paint.textSize = fontSizeSp * density // Convert sp to px
    return paint.measureText(this)
}

fun String.formatISO8601Timezone(): String {
    val formatter = eatDateISO8601TimezoneDateFormatter()
    val parsed = OffsetDateTime.parse(this) // Assumes it's valid ISO 8601
    return formatter.format(parsed)
}

fun Date.formatISO8601Timezone(): String {
    val formatter = eatDateISO8601TimezoneDateFormatter()
    val zonedDateTime = this.toInstant().atZone(ZoneId.systemDefault())
    return formatter.format(zonedDateTime)
}

fun Context.copyToClipboard(label: String, value: String) {
    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
    val clip = ClipData.newPlainText(label, value)
    clipboard.setPrimaryClip(clip)
}

fun String?.isValidEmail() = !isNullOrEmpty() && Patterns.EMAIL_ADDRESS.matcher(this).matches()

val String.labelized: String
    get() = this.split("_")
        .joinToString(" ") { it.replaceFirstChar { it.uppercase() } }

val String.underscoreToCapitalized: String
    get() = this.replace("_", " ")
        .lowercase()
        .replaceFirstChar { it.uppercase() }

val Any.customFieldType: CustomFieldType
    get() {
        return when (this) {
            is Boolean -> CustomFieldType.BOOLEAN
            is Int -> CustomFieldType.COUNTABLE
            is String -> CustomFieldType.TEXT
            is ArrayList<*> -> CustomFieldType.MULTI
            else -> CustomFieldType.TEXT
        }
    }

@Throws(Resources.NotFoundException::class)
fun Context.getDrawable(name: String): Drawable? {
    val id = resources.getIdentifier(name, "drawable", packageName)
    return ActivityCompat.getDrawable(this, id).takeIf { id != 0 }
}

fun Context.showConfirmationAlert(title: Int, description: Int, listener: () -> Unit) {
    val builder = AlertDialog.Builder(this)
    builder.setTitle(title)
        .setMessage(description)
        .setNegativeButton(android.R.string.cancel) { _, _ -> }
        .setPositiveButton(android.R.string.yes) { _, _ -> listener() }
        .show()
}

fun Context.showErrorAlert(title: String, description: String) {
    AlertDialog.Builder(this)
        .setTitle(title)
        .setMessage(description)
        .setPositiveButton(android.R.string.cancel) { _, _ -> }
        .show()
}

fun Context.showAlert(
    title: String,
    description: String,
    positiveButtonText: String? = null,
    negativeButtonText: String? = null,
    positiveButtonListener: (() -> Unit)
) {
    val builder = AlertDialog.Builder(this)
    builder.setTitle(title)
        .setMessage(description)
        .setNegativeButton(negativeButtonText) { _, _ -> }
        .setPositiveButton(positiveButtonText) { _, _ -> positiveButtonListener() }
        .show()
}

fun Context.showToast(text: String) {
    Toast.makeText(this, text, Toast.LENGTH_SHORT).show()
}

fun Context.openGoogleInBrowser(query: String) {
    val uri = getString(R.string.google_query_template, query).toUri()
    val intent = Intent(Intent.ACTION_VIEW, uri)
    startActivity(intent)
}

fun View.slideView(
    currentHeight: Int,
    newHeight: Int,
    animateHeight: Boolean,
    onEndAction: (() -> Unit)? = null
) {

    val slideAnimator = ValueAnimator
        .ofInt(currentHeight, newHeight)
        .setDuration(300)

    slideAnimator.addUpdateListener {
        val value = it.getAnimatedValue() as Int
        layoutParams = layoutParams.apply {
            if (animateHeight) height = value else width = value
        }
        this.requestLayout()
    }

    slideAnimator.doOnEnd {
        onEndAction?.invoke()
    }

    val animationSet = AnimatorSet()
    animationSet.interpolator = AccelerateDecelerateInterpolator()
    animationSet.playTogether(slideAnimator)
    animationSet.start()
}

fun Context.hideKeyboard(view: View) {
    val imm = this.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

fun Response<*>.notModified(): Boolean {
    return this.code() == HttpURLConnection.HTTP_NOT_MODIFIED
}

fun File.isGzip(): Boolean {
    val header = ByteArray(2)
    this.inputStream().use { it.read(header) }
    return header[0] == 0x1F.toByte() && header[1] == 0x8B.toByte()
}

fun CoroutineScope.cancelChildCoroutines() {
    this.coroutineContext[Job]?.cancelChildren()
}
