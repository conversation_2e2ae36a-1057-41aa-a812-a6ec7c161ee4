package com.eatapp.clementine.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.viewpager2.widget.ViewPager2
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.EatPagerAdapter
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.ui.reservation.ReservationDetailsFragment
import com.google.android.material.tabs.TabLayout

class ReservationTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : TabLayout(context, attrs, defStyleAttr) {

    private val minSelectedTabWidth = 120.px
    private var viewPager: ViewPager2? = null
    private var adapter: EatPagerAdapter? = null
    private var hasSharedTabs = false

    private var lastSelectedTabPosition = -1
    private var initIsDone = false

    init {
        setupTabLayout()
    }

    private fun setupTabLayout() {
        addOnTabSelectedListener(object : OnTabSelectedListener {
            override fun onTabSelected(tab: Tab?) {
                tabSelected(tab)
            }

            override fun onTabUnselected(tab: Tab?) {}

            override fun onTabReselected(tab: Tab?) {}
        })
    }

    private fun hasNewTabSelected(tab: Tab?): Boolean = tab?.position != lastSelectedTabPosition

    private fun tabSelected(tab: Tab?, scrollListener: Boolean = false) {
        if (!hasNewTabSelected(tab) && initIsDone) return
        lastSelectedTabPosition = tab?.position ?: 0
        updateTabs()
        handleTabSelection(tab, scrollListener)
    }

    fun changeTabsByScroll(guestProfile: Boolean) {
        tabSelected(getTabAt(if (guestProfile) 1 else 0), true)
    }

    fun guestClicked() = tabSelected(getTabAt(1))

    private fun handleTabSelection(tab: Tab?, scrollListener: Boolean) {
        val position = tab?.position ?: return
        val isSharedTab = (position == 0 || position == 1) && hasSharedTabs
        viewPager?.isUserInputEnabled = !isSharedTab
        tab.select()

        if (scrollListener) return

        if (!isSharedTab) {
            viewPager?.setCurrentItem(position, true)
            return
        }

        if (viewPager?.currentItem != 0) viewPager?.setCurrentItem(0, false)
        val detailFragment = adapter?.getItem(0) as ReservationDetailsFragment

        post {
            if (position == 0) detailFragment.scrollToDetails()
            else detailFragment.scrollToProfile()
        }
    }

    fun setupWithViewPager(viewPager: ViewPager2, adapter: EatPagerAdapter, hasSharedTabs: Boolean) {
        this.viewPager = viewPager
        this.adapter = adapter
        this.hasSharedTabs = hasSharedTabs

        setupSharedTabBehavior(viewPager)

        for (i in 0 until adapter.itemCount) {
            val tab = newTab()
            setupCustomTab(tab, i)
            addTab(tab)
        }

        post {
            tabSelected(getTabAt(0))
            initIsDone = true;
        }
    }

    private fun setupSharedTabBehavior(viewPager: ViewPager2) {
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                if (hasSharedTabs && position == 0 && selectedTabPosition == 1) return
                tabSelected(getTabAt(position))
            }
        })
    }

    @SuppressLint("InflateParams")
    private fun setupCustomTab(tab: Tab, position: Int) {
        val customView = LayoutInflater.from(context).inflate(R.layout.tab_item_reservation, null)
        val tabIcon = customView.findViewById<ImageView>(R.id.tab_icon)
        val tabText = customView.findViewById<TextView>(R.id.tab_text)

        adapter?.let { adapter ->
            tabIcon.setImageResource(adapter.getPageIcon(position))
            tabText.text = adapter.getPageTitle(position)
        }

        tab.customView = customView
    }

    private fun updateTabs() {
        val tabCount = tabCount

        if (tabCount == 0) return

        post {
            if (width <= 0) return@post

            val (selectedTabWidth, unselectedTabWidth) = getTabWidth()

            for (i in 0 until tabCount) {
                val customView = getTabAt(i)?.customView
                val isSelected = i == selectedTabPosition

                customView?.apply {
                    val lp = layoutParams
                    lp.width = if (isSelected) selectedTabWidth else unselectedTabWidth
                    layoutParams = lp

                    val tabIcon = findViewById<ImageView>(R.id.tab_icon)
                    val tabText = findViewById<TextView>(R.id.tab_text)

                    if (isSelected) {
                        tabText?.visibility = VISIBLE
                        tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorPrimary))
                        tabText?.setTextColor(ContextCompat.getColor(context, R.color.colorPrimary))
                    } else {
                        tabText?.visibility = GONE
                        tabIcon?.setColorFilter(ContextCompat.getColor(context, R.color.colorDark50))
                    }
                }
            }
        }
    }

    private fun getTabWidth(): Pair<Int, Int> {
        val dynamicSize = width / tabCount
        val selectedTabWidth = maxOf(dynamicSize, minSelectedTabWidth)

        val remainingWidth = width - selectedTabWidth
        val unselectedTabCount = tabCount - 1
        val unselectedTabWidth = if (unselectedTabCount > 0) {
            maxOf(remainingWidth / unselectedTabCount, 48.px)
        } else {
            width
        }

        return Pair(selectedTabWidth, unselectedTabWidth)
    }

    override fun onDetachedFromWindow() {
        super.onDetachedFromWindow()
        viewPager = null
        adapter = null
    }

}