package com.eatapp.clementine.data.network.response.guest


import android.os.Parcelable
import androidx.databinding.BaseObservable
import androidx.databinding.Bindable
import androidx.databinding.library.baseAdapters.BR
import com.eatapp.clementine.data.database.entities.GuestEntity
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignmentModel
import com.eatapp.clementine.internal.SelectorItem
import com.google.gson.GsonBuilder
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

@Parcelize
data class Guest(

    @SerializedName("id")
    var id: String,
    @SerializedName("relationships")
    var relationships: GuestRelationships?,
    @SerializedName("attributes")
    var attributes: GuestAttributes?,
    var tags: MutableList<SelectorItem>?,
    var taggings: MutableList<Tagging>?,

    var header: String? = "",
    var isHeader: Boolean = false,
    @Bindable var unreadMessagesCount: Int = 0,

    var voucherAssignments: MutableList<VoucherAssignmentModel>? = null

) : Parcelable, BaseObservable() {

    constructor(header: String, isHeader: Boolean) : this(
        "",
        null, null, null, null
    ) {
        this.header = header
        this.isHeader = isHeader
    }

    constructor() : this("", false) {
        this.attributes = GuestAttributes()
    }

    constructor(
        firstName: String?,
        lastName: String?,
        email: String?,
        phone: String?
    ) : this("", null, GuestAttributes(firstName = firstName, lastName = lastName, email = email, phone = phone), null, null)

    @Transient
    @IgnoredOnParcel
    private var changeListener: (() -> Unit)? = null

    @IgnoredOnParcel
    val fullName: String
        get() = "$firstName $lastName"
    
    var anniversary: Date?
        get() {
            return attributes?.anniversary
        }
        set(v) {
            if (v != attributes?.anniversary) notifyChangeListener()
            attributes?.anniversary = v
            notifyPropertyChanged(BR.anniversaryS)
        }

    var birthday: Date?
        get() {
            return attributes?.birthday
        }
        set(v) {
            if (v != attributes?.birthday) notifyChangeListener()
            attributes?.birthday = v
            notifyPropertyChanged(BR.birthdayS)
        }

    val anniversaryS: String?
        @Bindable get() {
            return if (attributes?.anniversary != null)
                SimpleDateFormat("dd MMMM, yyyy", Locale.US).format(attributes?.anniversary) else ""
        }

    val birthdayS: String?
        @Bindable get() {
            return if (attributes?.birthday != null)
                SimpleDateFormat("dd MMMM, yyyy", Locale.US).format(attributes?.birthday) else ""
        }

    val updatedAt: Date?
        get() {
            return attributes?.updatedAt
        }

    var email: String?
        get() {
            return attributes?.email
        }
        set(v) {
            if (v != attributes?.email) notifyChangeListener()
            attributes?.email = v ?: ""
        }

    var firstName: String?
        get() {
            return attributes?.firstName ?: ""
        }
        set(v) {
            if (v != attributes?.firstName) notifyChangeListener()
            attributes?.firstName = v ?: ""
        }

    var lastName: String?
        get() {
            return attributes?.lastName ?: ""
        }
        set(v) {
            if (v != attributes?.lastName) notifyChangeListener()
            attributes?.lastName = v ?: ""
        }

    var notes: String?
        @Bindable get() {
            return attributes?.notes
        }
        set(v) {
            if (v != attributes?.notes) notifyChangeListener()
            attributes?.notes = v ?: ""
        }

    var phone: String?
        get() {
            return attributes?.phone
        }
        set(v) {
            if (v != attributes?.phone) notifyChangeListener()
            attributes?.phone = v ?: ""
        }

    var marketingAccepted: Boolean
        get() {
            return attributes?.marketingAccepted ?: false
        }
        set(v) {
            if (v != attributes?.marketingAccepted) notifyChangeListener()
            attributes?.marketingAccepted = v
        }

    val cancellationCount: Int
        get() {
            return attributes?.visitData?.canceledCount ?: 0
        }

    val noShowCount: Int
        get() {
            return attributes?.visitData?.noShowCount ?: 0
        }

    val deniedCount: Int
        get() {
            return attributes?.visitData?.deniedCount ?: 0
        }

    val upcomingCount: Int
        get() {
            return attributes?.visitData?.upcomingReservationsCount ?: 0
        }

    val visitCount: Int
        get() {
            return attributes?.visitData?.visitCount ?: 0
        }

    val posTicketsCount: Int
        get() {
            return attributes?.posData?.posTicketsCount ?: 0
        }

    val totalSpend: Double?
        get() {
            return attributes?.posData?.totalSpend
        }

    val averageSpendPerVisit: Double?
        get() {
            return attributes?.posData?.averageSpendPerVisit
        }

    val averageSpendPerCover: Double?
        get() {
            return attributes?.posData?.averageSpendPerCover
        }

    val reviewRating: Double
        get() {
            return attributes?.visitData?.reviewsAverageRating ?: 0.0
        }

    fun getGoogleQuery() = "$fullName ${email ?: ""} ${attributes?.organisation ?: ""}"

    fun setOnChangeListener(listener: () -> Unit) {
        changeListener = listener
    }

    private fun notifyChangeListener() = changeListener?.invoke()


    fun clone(): Guest {
        val builder = GsonBuilder()
            .setObjectToNumberStrategy {
                // Read the JSON number as string to inspect its format
                val numberAsString = it.nextString()

                if (numberAsString.contains('.')) numberAsString.toDouble()
                else numberAsString.toLong()
            }
            .serializeNulls()
        val gson = builder.create()
        val stringProject = gson.toJson(this, Guest::class.java)
        return gson.fromJson(stringProject, Guest::class.java)
    }

    fun toGuestBody(
        addedTags: List<String>?,
        removedTags: List<String>?,
        addedPoints: Int? = null,
        removedPoints: Int? = null
    ): GuestBody {
        return GuestBody(
            firstName?.capitalize(),
            lastName?.capitalize(),
            anniversary,
            birthday,
            email,
            phone,
            notes,
            marketingAccepted,
            addedTags,
            removedTags,
            attributes?.customFields ?: mutableMapOf(),
            addedPoints,
            removedPoints
        )
    }

    fun toGuest(): GuestEntity {
        return GuestEntity(
            guestId = this.id,
            anniversary = this.attributes?.anniversary,
            birthday = this.attributes?.birthday,
            customFields = this.attributes?.customFields,
            email = this.attributes?.email,
            firstName = this.attributes?.firstName,
            lastName = this.attributes?.lastName,
            marketingAccepted = this.attributes?.marketingAccepted ?: false,
            notes = this.attributes?.notes,
            organisation = this.attributes?.organisation,
            phone = this.attributes?.phone,
            posData = this.attributes?.posData,
            taggingEntities = this.taggings?.map { it.toEntity() },
            updatedAt = this.attributes?.updatedAt,
            restaurantId = this.attributes?.restaurantId ?: "",
            loyaltyPoints = this.attributes?.loyaltyPoints ?: 0,
            demo = this.attributes?.demo,
            content = ""
        )
    }

    fun updateWith(body: GuestBody) {
        if (this.attributes == null) {
            this.attributes = GuestAttributes()
        }

        this.attributes?.apply {
            firstName = body.firstName
            lastName = body.lastName
            anniversary = body.anniversary
            birthday = body.birthday
            email = body.email
            phone = body.phone
            notes = body.notes
            marketingAccepted = body.marketingAccepted
            customFields = body.customFields.toMutableMap()
        }
    }
}