package com.eatapp.clementine.data.network.response.payment

import android.os.Parcelable
import com.eatapp.clementine.R
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class GatewayAction(
    val amount: String,
    val date: String?,
    val id: String?,
    @SerializedName("response_code")
    val responseCode: String,
    @SerializedName("response_message")
    val responseMessage: String,
    val type: String
) : Parcelable

enum class GatewayActionStatus(private val iconId: Int, val readableStatus: Int) {
    AUTHORIZATION(
        R.drawable.ic_payment_status_authorised,
        R.string.gateway_action_status_authorized
    ),
    AUTHORIZED(R.drawable.ic_payment_status_authorised, R.string.gateway_action_status_authorized),
    CAPTURED(R.drawable.ic_payment_status_captured, R.string.gateway_action_status_captured),
    CAPTURE(R.drawable.ic_payment_status_captured, R.string.gateway_action_status_captured),
    VOID(R.drawable.ic_payment_status_voided, R.string.gateway_action_status_voided),
    REFUND(R.drawable.ic_payment_status_refunded, R.string.gateway_action_status_refunded),
    CANCEL(R.drawable.ic_icon_status_canceled, R.string.gateway_action_status_canceled),
    DECLINED(R.drawable.ic_icon_status_canceled, R.string.gateway_action_status_declined);

    companion object {
        fun icon(status: String): Int {
            return try {
                valueOf(status.uppercase()).iconId
            } catch (e: Exception) {
                R.drawable.ic_icon_status_canceled
            }
        }
    }
}
